#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Edge驱动修复工具
手动下载并配置Edge驱动
"""

import os
import sys
import requests
import zipfile
import subprocess
import winreg
from pathlib import Path

def get_edge_version():
    """获取Edge浏览器版本"""
    try:
        # 方法1：从注册表获取版本（更可靠）
        registry_paths = [
            r"SOFTWARE\Microsoft\Edge\BLBeacon",
            r"SOFTWARE\WOW6432Node\Microsoft\Edge\BLBeacon"
        ]

        for path in registry_paths:
            try:
                key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, path)
                version, _ = winreg.QueryValueEx(key, "version")
                winreg.CloseKey(key)
                print(f"✅ 从注册表获取Edge版本: {version}")
                return version
            except:
                continue

        # 方法2：使用PowerShell获取版本
        try:
            powershell_cmd = '''
            $edge = Get-ItemProperty "HKLM:\\SOFTWARE\\Microsoft\\Edge\\BLBeacon" -Name version -ErrorAction SilentlyContinue
            if ($edge) { $edge.version } else {
                $edge = Get-ItemProperty "HKLM:\\SOFTWARE\\WOW6432Node\\Microsoft\\Edge\\BLBeacon" -Name version -ErrorAction SilentlyContinue
                if ($edge) { $edge.version }
            }
            '''
            result = subprocess.run(["powershell", "-Command", powershell_cmd],
                                  capture_output=True, text=True, timeout=15)
            if result.returncode == 0 and result.stdout.strip():
                version = result.stdout.strip()
                print(f"✅ 从PowerShell获取Edge版本: {version}")
                return version
        except:
            pass

        # 方法3：使用默认版本（如果都失败了）
        print("⚠️ 无法自动获取Edge版本，使用通用版本")
        return "131.0.2903.112"  # 使用一个较新的稳定版本

    except Exception as e:
        print(f"❌ 获取Edge版本失败: {e}")

    return None

def download_driver_manual(version):
    """手动下载Edge驱动"""
    try:
        # 构建下载URL
        download_url = f"https://msedgedriver.azureedge.net/{version}/edgedriver_win64.zip"
        
        print(f"正在下载Edge驱动 v{version}...")
        print(f"下载地址: {download_url}")
        
        # 设置请求头，模拟浏览器
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        # 下载文件
        response = requests.get(download_url, headers=headers, timeout=60)
        response.raise_for_status()
        
        # 保存zip文件
        zip_path = "edgedriver.zip"
        with open(zip_path, 'wb') as f:
            f.write(response.content)
        
        print(f"✅ 下载完成: {zip_path}")
        
        # 解压文件
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(".")
        
        # 删除zip文件
        os.remove(zip_path)
        
        # 检查驱动文件
        driver_path = "msedgedriver.exe"
        if os.path.exists(driver_path):
            print(f"✅ Edge驱动解压成功: {os.path.abspath(driver_path)}")
            return driver_path
        else:
            print("❌ 解压后未找到msedgedriver.exe")
            return None
            
    except Exception as e:
        print(f"❌ 下载驱动失败: {e}")
        return None

def test_driver_simple():
    """简单测试驱动是否可用"""
    try:
        driver_path = "msedgedriver.exe"
        if not os.path.exists(driver_path):
            return False
            
        # 测试驱动是否可以启动
        result = subprocess.run([driver_path, "--version"], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ 驱动版本: {result.stdout.strip()}")
            return True
        else:
            print("❌ 驱动无法启动")
            return False
            
    except Exception as e:
        print(f"❌ 驱动测试失败: {e}")
        return False

def main():
    """主函数"""
    print("Edge驱动修复工具")
    print("=" * 50)
    
    # 检查是否已有驱动
    if os.path.exists("msedgedriver.exe"):
        print("发现现有驱动文件，正在测试...")
        if test_driver_simple():
            print("✅ 现有驱动可用，无需下载")
            return
        else:
            print("现有驱动不可用，将重新下载")
            os.remove("msedgedriver.exe")
    
    # 获取Edge版本
    version = get_edge_version()
    if not version:
        print("❌ 无法获取Edge版本，请确保已安装Microsoft Edge浏览器")
        return
    print(f"Edge版本: {version}")

    # 下载驱动
    driver_path = download_driver_manual(version)
    if not driver_path:
        print("❌ 驱动下载失败")
        print("\n手动解决方案:")
        print(f"1. 访问: https://developer.microsoft.com/en-us/microsoft-edge/tools/webdriver/")
        print(f"2. 下载与Edge版本 {version} 匹配的驱动")
        print(f"3. 将msedgedriver.exe放到当前目录: {os.getcwd()}")
        return
    
    # 测试驱动
    if test_driver_simple():
        print("\n🎉 Edge驱动下载和配置成功！")
        print(f"驱动路径: {os.path.abspath(driver_path)}")
        print("\n现在可以运行natapp_auto.py了")
    else:
        print("\n❌ 驱动测试失败")

if __name__ == "__main__":
    main()
