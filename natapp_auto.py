import os
import sys
import time
import random
import subprocess
import argparse
from selenium import webdriver
from selenium.webdriver.edge.service import Service
from selenium.webdriver.edge.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
# 移除webdriver_manager依赖，使用Selenium内置的驱动管理
from dotenv import load_dotenv
from loguru import logger

class NatappAutoTunnel:
    def __init__(self):
        self.setup_logger()
        self.load_credentials()
        self.setup_driver()

    def setup_logger(self):
        """设置日志配置"""
        logger.remove()
        logger.add(sys.stderr, level="INFO")
        logger.add("natapp_auto.log", rotation="1 day", retention="7 days", level="DEBUG")

    def load_credentials(self):
        """加载登录凭证"""
        load_dotenv()
        self.username = os.getenv('NATAPP_USERNAME')
        self.password = os.getenv('NATAPP_PASSWORD')
        
        if not self.username or not self.password:
            logger.error("未找到登录凭证，请设置环境变量")
            raise ValueError("请在.env文件中设置NATAPP_USERNAME和NATAPP_PASSWORD")

    def setup_driver(self):
        """设置Edge浏览器驱动"""
        try:
            logger.info("开始设置Edge驱动")
            edge_options = Options()
            edge_options.add_argument('--headless')
            edge_options.add_argument('--disable-gpu')
            edge_options.add_argument('--no-sandbox')
            edge_options.add_argument('--disable-dev-shm-usage')
            edge_options.add_argument('--disable-blink-features=AutomationControlled')
            edge_options.add_experimental_option('excludeSwitches', ['enable-automation'])
            edge_options.add_experimental_option('useAutomationExtension', False)

            # 使用Selenium内置的驱动管理
            service = Service()

            self.driver = webdriver.Edge(service=service, options=edge_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.driver.set_window_size(1366, 768)
            self.wait = WebDriverWait(self.driver, 60)
            logger.success("Edge驱动设置成功")

        except Exception as e:
            logger.error(f"设置Edge驱动时出错: {str(e)}")
            raise

    def random_sleep(self, min_seconds=1, max_seconds=3):
        """随机等待一段时间"""
        time.sleep(random.uniform(min_seconds, max_seconds))

    def login(self):
        """执行登录操作"""
        try:
            logger.info("开始登录过程")
            self.driver.get("https://natapp.cn/login")
            self.random_sleep(2, 4)
            
            username_input = self.wait.until(EC.presence_of_element_located((By.NAME, "login")))
            password_input = self.driver.find_element(By.NAME, "password")
            
            self.driver.execute_script(f'arguments[0].value = "{self.username}";', username_input)
            self.driver.execute_script(f'arguments[0].value = "{self.password}";', password_input)
            
            # 勾选同意协议
            try:
                agreement = self.wait.until(EC.presence_of_element_located((By.ID, "agree")))
                if not agreement.is_selected():
                    self.driver.execute_script("arguments[0].click();", agreement)
                    self.random_sleep(0.5, 1)
            except Exception:
                pass
            
            login_button = self.wait.until(EC.element_to_be_clickable((By.XPATH, "//button[@type='submit']")))
            self.driver.execute_script("arguments[0].click();", login_button)
            self.random_sleep(3, 5)
            
            # 验证登录
            self.wait.until(EC.presence_of_element_located((By.XPATH, "//a[contains(text(), '我的隧道')]")))
            logger.success("登录成功")
            return True
            
        except Exception as e:
            logger.error(f"登录过程出错: {str(e)}")
            raise

    def get_or_create_tunnel(self, protocol="http", port="8080"):
        """获取或创建隧道Token"""
        try:
            logger.info(f"开始获取{protocol}隧道Token")
            
            # 访问隧道列表页面
            self.driver.get("https://natapp.cn/tunnel/lists")
            self.random_sleep(2, 3)
            
            # 查找现有隧道
            try:
                table = self.wait.until(EC.presence_of_element_located((By.CLASS_NAME, "table")))
                protocol_text = "Web" if protocol == "http" else protocol.upper()
                rows = table.find_elements(By.TAG_NAME, "tr")
                
                for row in rows:
                    if protocol_text in row.text:
                        try:
                            token_element = row.find_element(By.CLASS_NAME, "clickCopy")
                            token = token_element.get_attribute("data-clipboard-text")
                            if token:
                                logger.success(f"找到现有{protocol}隧道Token: {token}")
                                return token
                        except:
                            pass
                            
            except TimeoutException:
                pass
            
            # 如果没有找到，创建新隧道
            logger.info(f"未找到{protocol}隧道，开始创建新隧道")
            return self.create_tunnel(protocol, port)
            
        except Exception as e:
            logger.error(f"获取隧道Token失败: {str(e)}")
            raise

    def create_tunnel(self, protocol="http", port="8080"):
        """创建新的免费隧道"""
        try:
            self.driver.get("https://natapp.cn/tunnel/buy/free")
            self.random_sleep(2, 3)
            
            # 设置隧道名称
            name_input = self.wait.until(EC.presence_of_element_located((By.NAME, "name")))
            tunnel_name = f"{protocol}{int(time.time())%1000}"
            self.driver.execute_script(f'arguments[0].value = "{tunnel_name}";', name_input)
            
            # 选择协议
            protocol_select = self.wait.until(EC.presence_of_element_located((By.NAME, "protocol")))
            self.driver.execute_script(f'arguments[0].value = "{protocol}";', protocol_select)
            
            # 设置本地端口
            local_port = "22" if protocol == "tcp" else port
            port_input = self.wait.until(EC.presence_of_element_located((By.NAME, "local_port")))
            self.driver.execute_script(f'arguments[0].value = "{local_port}";', port_input)
            
            # 点击购买按钮
            buy_button = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(@class, 'btn-success') and contains(text(), '免费购买')]"))
            )
            self.driver.execute_script("arguments[0].click();", buy_button)
            self.random_sleep(3, 5)
            
            # 检查创建结果
            if "已购买" in self.driver.page_source or "成功" in self.driver.page_source:
                logger.success(f"免费{protocol}隧道创建成功")
                # 重新获取Token
                return self.get_or_create_tunnel(protocol, port)
            else:
                raise Exception("创建隧道失败")
                
        except Exception as e:
            logger.error(f"创建隧道失败: {str(e)}")
            raise

    def run_natapp(self, token):
        """运行natapp进行内网穿透"""
        try:
            if not os.path.exists("natapp.exe"):
                logger.error("在当前目录中找不到natapp.exe")
                raise FileNotFoundError("请确保natapp.exe程序与本脚本在同一目录")
            
            cmd = ["natapp.exe", f"-authtoken={token}"]
            logger.info(f"正在使用Token [{token}] 启动内网穿透...")
            
            # 启动natapp进程
            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            # 实时输出日志
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    print(output.strip())
                    
            return_code = process.poll()
            if return_code == 0:
                logger.success("内网穿透启动成功")
            else:
                logger.error(f"内网穿透启动失败，返回码: {return_code}")
                
        except Exception as e:
            logger.error(f"运行natapp出错: {str(e)}")
            raise

    def close(self):
        """关闭浏览器"""
        if hasattr(self, 'driver'):
            self.driver.quit()
            logger.info("浏览器已关闭")

def main():
    parser = argparse.ArgumentParser(description='Natapp自动隧道工具')
    parser.add_argument('--protocol', '-p', choices=['http', 'tcp', 'udp'], 
                       default='http', help='隧道协议 (默认: http)')
    parser.add_argument('--port', '-P', type=str, default='8080', 
                       help='本地端口 (默认: 8080)')
    parser.add_argument('--token-only', '-t', action='store_true', 
                       help='仅获取Token，不启动natapp')
    
    args = parser.parse_args()
    
    tunnel = None
    try:
        tunnel = NatappAutoTunnel()
        tunnel.login()
        
        # 获取或创建隧道Token
        token = tunnel.get_or_create_tunnel(args.protocol, args.port)
        
        if not token:
            raise Exception("获取Token失败")
            
        print(f"\n获取到的{args.protocol}隧道Token: {token}\n")
        
        if args.token_only:
            logger.info("仅获取Token模式，程序结束")
            return token
            
        # 启动natapp
        tunnel.run_natapp(token)
        
    except Exception as e:
        logger.error(f"程序执行出错: {str(e)}")
        return None
    finally:
        if tunnel:
            tunnel.close()

if __name__ == "__main__":
    main()