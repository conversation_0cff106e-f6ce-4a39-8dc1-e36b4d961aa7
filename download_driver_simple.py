#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的Edge驱动下载脚本
"""

import os
import requests
import zipfile

def download_edge_driver():
    """下载Edge驱动"""
    print("Edge驱动下载工具")
    print("=" * 50)
    
    # 检查是否已有驱动
    if os.path.exists("msedgedriver.exe"):
        print("✅ 发现现有驱动文件: msedgedriver.exe")
        choice = input("是否要重新下载？(y/N): ").lower()
        if choice != 'y':
            print("保持现有驱动文件")
            return True
        os.remove("msedgedriver.exe")
    
    # 尝试下载几个常见版本的驱动
    versions = [
        "131.0.2903.112",
        "130.0.2849.68", 
        "129.0.2792.89",
        "128.0.2739.79"
    ]
    
    for version in versions:
        print(f"\n尝试下载Edge驱动 v{version}...")
        url = f"https://msedgedriver.azureedge.net/{version}/edgedriver_win64.zip"
        
        try:
            print(f"下载地址: {url}")
            
            # 设置请求头
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            # 下载文件
            response = requests.get(url, headers=headers, timeout=30)
            response.raise_for_status()
            
            # 保存zip文件
            zip_path = "edgedriver.zip"
            with open(zip_path, 'wb') as f:
                f.write(response.content)
            
            print(f"✅ 下载完成: {zip_path}")
            
            # 解压文件
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(".")
            
            # 删除zip文件
            os.remove(zip_path)
            
            # 检查驱动文件
            if os.path.exists("msedgedriver.exe"):
                print(f"✅ Edge驱动解压成功: {os.path.abspath('msedgedriver.exe')}")
                print(f"✅ 使用的版本: {version}")
                return True
            else:
                print("❌ 解压后未找到msedgedriver.exe")
                continue
                
        except Exception as e:
            print(f"❌ 版本 {version} 下载失败: {e}")
            continue
    
    print("\n❌ 所有版本都下载失败")
    print("\n手动解决方案:")
    print("1. 访问: https://developer.microsoft.com/en-us/microsoft-edge/tools/webdriver/")
    print("2. 手动下载适合您Edge版本的驱动")
    print("3. 将msedgedriver.exe放到当前目录")
    return False

if __name__ == "__main__":
    success = download_edge_driver()
    if success:
        print("\n🎉 现在可以运行natapp_auto.py了！")
    else:
        print("\n❌ 请手动下载驱动后再试")
