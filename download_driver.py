#!/usr/bin/env python3
"""
手动下载Edge驱动的脚本
"""

import os
import sys
import requests
import zipfile
import subprocess
from pathlib import Path

def get_edge_version():
    """获取系统中Edge浏览器的版本"""
    try:
        # Windows系统获取Edge版本的方法
        result = subprocess.run([
            'reg', 'query', 
            r'HKEY_CURRENT_USER\Software\Microsoft\Edge\BLBeacon', 
            '/v', 'version'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            for line in result.stdout.split('\n'):
                if 'version' in line:
                    version = line.split()[-1]
                    return version
    except:
        pass
    
    # 备用方法：检查Edge安装目录
    edge_paths = [
        r"C:\Program Files (x86)\Microsoft\Edge\Application",
        r"C:\Program Files\Microsoft\Edge\Application"
    ]
    
    for path in edge_paths:
        if os.path.exists(path):
            for item in os.listdir(path):
                if item.replace('.', '').isdigit():  # 版本号目录
                    return item
    
    return None

def download_edge_driver(version=None):
    """下载对应版本的Edge驱动"""
    if not version:
        version = get_edge_version()
        if not version:
            print("无法检测Edge版本，请手动指定版本号")
            return False
    
    print(f"检测到Edge版本: {version}")
    
    # 构建下载URL
    major_version = version.split('.')[0]
    download_url = f"https://msedgedriver.azureedge.net/{version}/edgedriver_win64.zip"
    
    print(f"下载URL: {download_url}")
    
    try:
        # 下载驱动
        print("正在下载Edge驱动...")
        response = requests.get(download_url, timeout=30)
        response.raise_for_status()
        
        # 保存zip文件
        zip_path = "edgedriver.zip"
        with open(zip_path, 'wb') as f:
            f.write(response.content)
        
        # 解压文件
        print("正在解压驱动文件...")
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(".")
        
        # 删除zip文件
        os.remove(zip_path)
        
        # 检查是否成功
        if os.path.exists("msedgedriver.exe"):
            print("Edge驱动下载成功！")
            print("文件位置: msedgedriver.exe")
            return True
        else:
            print("解压后未找到msedgedriver.exe文件")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"下载失败: {e}")
        return False
    except Exception as e:
        print(f"处理失败: {e}")
        return False

def main():
    print("Edge驱动下载工具")
    print("=" * 30)
    
    # 检查是否已存在驱动
    if os.path.exists("msedgedriver.exe"):
        print("当前目录已存在 msedgedriver.exe")
        choice = input("是否重新下载？(y/N): ").lower()
        if choice != 'y':
            return
    
    # 获取版本并下载
    version = get_edge_version()
    if version:
        print(f"检测到Edge版本: {version}")
        if download_edge_driver(version):
            print("\n下载完成！现在可以运行natapp脚本了。")
        else:
            print("\n下载失败，请尝试手动下载：")
            print("1. 访问 https://developer.microsoft.com/en-us/microsoft-edge/tools/webdriver/")
            print("2. 下载对应版本的驱动")
            print("3. 解压后将 msedgedriver.exe 放到当前目录")
    else:
        print("无法检测Edge版本，请手动下载驱动")
        print("访问: https://developer.microsoft.com/en-us/microsoft-edge/tools/webdriver/")

if __name__ == "__main__":
    main()
