# Natapp Token 获取工具

这个工具用于自动获取 Natapp 的 authtoken。

## 环境要求

- Python 3.7+
- Chrome 浏览器
- pip（Python 包管理器）

## 安装步骤

1. 安装依赖包：

```bash
pip install -r requirements.txt
```

2. 创建 `.env` 文件并设置以下环境变量：

```
NATAPP_USERNAME=你的用户名
NATAPP_PASSWORD=你的密码
```

## 使用方法

直接运行脚本：

```bash
python get_natapp_token.py
```

## 功能特点

- 自动登录 Natapp
- 获取 authtoken
- 支持日志记录
- 无头浏览器模式
- 异常处理机制

## 注意事项

1. 请确保您的登录凭证正确
2. 需要稳定的网络连接
3. 首次运行时会自动下载 Chrome 驱动
4. 所有操作日志会保存在 `natapp_token.log` 文件中

## 安全提示

- 不要将 `.env` 文件提交到版本控制系统
- 定期更改您的密码
- 不要将 token 分享给他人

## 常见问题

1. 如果遇到 Chrome 驱动问题，请确保已安装最新版本的 Chrome 浏览器
2. 如果登录失败，请检查用户名和密码是否正确
3. 如果获取 token 失败，可能是网络问题或网站结构变化，请查看日志文件获取详细信息
