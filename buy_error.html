<html><head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0,maximum-scale=1.0, user-scalable=no">
    <title>NATAPP - </title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <!-- Bootstrap framework -->


<link rel="stylesheet" href="https://cdn.natapp.cn/assets/bootstrap/css/bootstrap.css?version=20250414">

<!-- tooltips-->
<link rel="stylesheet" href="https://cdn.natapp.cn/assets/lib/jBreadcrumbs/css/BreadCrumb.css?version=20250414">


<!-- code prettify -->
<link rel="stylesheet" href="https://cdn.natapp.cn/assets/lib/google-code-prettify/prettify.css?version=20250414">
<!-- sticky notifications -->
<link rel="stylesheet" href="https://cdn.natapp.cn/assets/lib/sticky/sticky.css?version=20250414">
<!-- aditional icons -->
<link rel="stylesheet" href="https://cdn.natapp.cn/assets/natapp/img/splashy/splashy.css?version=20250414">
<!-- flags -->
<link rel="stylesheet" href="https://cdn.natapp.cn/assets/natapp/img/flags/flags.css?version=20250414">

<!-- hint.css -->
<link rel="stylesheet" href="https://cdn.natapp.cn/assets/lib/hint_css/hint.min.css?version=20250414">
<!-- main styles -->
<link rel="stylesheet" href="https://cdn.natapp.cn/assets/natapp/css/style.css?version=20250414">
<!-- theme color-->
<link rel="stylesheet" href="https://cdn.natapp.cn/assets/natapp/css/common.css?version=20250414">

<link id="link_theme" rel="stylesheet" href="https://cdn.natapp.cn/assets/natapp/css/green.css?version=20250414">



<link rel="stylesheet" href="https://cdn.natapp.cn/assets/css/common.css?version=20250414">
<link rel="stylesheet" href="https://cdn.natapp.cn/assets/lib/font-awesome/css/font-awesome.min.css?version=20250414">



<!-- favicon -->
<link rel="shortcut icon" href="https://cdn.natapp.cn/assets/favicon.ico?version=20250414">



<script src="https://cdn.natapp.cn/assets/lib/jquery-3.6.0.min.js?version=20250414"></script>



</head>
<body class="full_width ptrn_b menu_hover">


<div id="maincontainer" class="clearfix">
    <header>
        <nav class="navbar navbar-default navbar-fixed-top" role="navigation">
    <div class="navbar-inner">
        <div class="container-fluid">
            <div class="logo-manage"><img src="https://cdn.natapp.cn/assets/natapp/img/logo.png?version=20250414"></div>
            <a class="brand pull-left" href="https://natapp.cn">NATAPP.cn</a>
                        <ul class="nav navbar-nav user_menu pull-right">
                <li><a href="https://natapp.cn">首页</a></li>

                <li><a href="https://natapp.cn#download">客户端下载</a></li>
                <li><a href="https://natapp.cn/article">教程/文档</a></li>
                <li><a href="https://natapp.cn#contact">联系</a></li>

                                    <li class="divider-vertical hidden-sm hidden-xs"></li>
                    <li class="hidden-phone hidden-tablet">
                        <div class="nb_boxes clearfix">
                            <a href="https://natapp.cn/message" class="label hint--bottom" data-hint="您有 0 条未读消息">0 <i class="splashy-mail_light"></i></a>
                          </div>
                    </li>
                    <li><a href="https://natapp.cn/user/recharge">充值</a></li>
                <li class="dropdown">
                    <a href="#" class="dropdown-toggle" data-toggle="dropdown"><img src="https://natapp.cn/assets/admin/img/user_avatar.png" alt="" class="user_avatar">dawannn <b class="caret"></b></a>
                    <ul class="dropdown-menu">

                        <li><a href="https://natapp.cn/user/recharge">充值</a></li>

                        <li class="divider"></li>
                        <li><a href="https://natapp.cn/user/reset">修改密码</a></li>

                        <li class="divider"></li>
                        <li><a href="https://natapp.cn/logout">退出</a></li>
                    </ul>
                </li>

                            </ul>
        </div>
    </div>
</nav>

    </header>


    <div id="contentwrapper">
        <div class="main_content">
           


                    <div class="row">
        <div id="jCrumbs1" class="breadCrumb module">
            <ul>
                <li>
                    <a href="/"><i class="glyphicon glyphicon-home"></i></a>
                </li>
                <li>
                    <a href="https://natapp.cn/member/dashborad">会员中心</a>


                </li>
                <li>
                    <a href="https://natapp.cn/tunnel/lists">我的隧道</a>


                </li>
                <li>
                    <a href="https://natapp.cn/tunnel/buy">购买隧道</a>


                </li>
            </ul>
        </div>
    </div>
    <div class="row">
        <div class="col-sm-12 ui-border ui-block">
            <h3 class="heading">购买免费型隧道</h3>
            <div class="well">
                一个注册用户可免费拥有3条不同协议的隧道,隧道有效期2天,到期后自动删除,可再次免费购买
            </div>
            <div class="spacer"></div>
            <div class="alert alert-danger">
        <a class="close" data-dismiss="alert">×</a>
        已购买http隧道,无需再次购买    </div>
            <form class="form-horizontal skipSubmitLoading" action="" method="POST" name="buy_form" id="buy_form" novalidate="novalidate">
                <input type="hidden" name="_token" value="7zTXi0RPKtH6jGaMc7MyH3GPKbciPAvOdvpV3DhP">
                <fieldset>
                    <div class="form-group">
                        <label class="control-label col-sm-2">名称:</label>
                        <div class="col-sm-10">
                            <div class="row">
                                <div class="col-sm-4 input_control">


                                    <input id="name" name="name" class="form-control" type="text" value="我的免费隧道">


                                </div>
                                <div class="col-sm-4 tipBox">
                                    <div class="message-tip hints"></div>
                                    <div class="ok" style="display: none"><i class="i-icon icon-ok "></i></div>
                                                                            <div class="error" style="display: none"><i class="i-icon icon-error "></i><span class="txt"></span></div>
                                    
                                </div>
                            </div>
                            <span class="help-block"></span>
                        </div>

                    </div>
                    <div class="form-group">
                        <label class="control-label col-sm-2">隧道协议:</label>
                        <div class="col-sm-10">
                            <div class="row">
                                <div class="col-sm-2">
                                    <select id="protocol" class="form-control" name="protocol"><option value="http">Web</option><option value="tcp">TCP</option><option value="udp">UDP</option></select>


                                </div>
                            </div>
                            <span class="help-block">
                            Web: 普通型http(s)隧道穿透,用于搭建网站,微信开发等穿透到本地web服务.
                            <br>

                            TCP: 端口转发 应用于SSH,数据库,远程桌面,GAME等基于TCP连接的一切应用任您想象~<br>
                            UDP: 端口转发 应用于游戏,远程开机等基于UDP协议的一切应用<br>
                            选定后不可更改
                            </span>
                        </div>

                    </div>
                    <div class="form-group">
                        <label class="control-label col-sm-2">域名/远程端口:</label>
                        <div class="col-sm-10">
                            <p class="form-control-static">
                                系统随机分配
                            </p>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="control-label col-sm-2">本地端口:</label>
                        <div class="col-sm-10">
                            <div class="row">
                                <div class="col-sm-2 input_control">


                                    <input id="local_port" name="local_port" class="form-control" type="text" value="80">


                                </div>
                                <div class="col-sm-4 tipBox">
                                    <div class="message-tip hints"></div>
                                    <div class="ok" style="display: none"><i class="i-icon icon-ok "></i></div>
                                                                            <div class="error" style="display: none"><i class="i-icon icon-error "></i><span class="txt"></span></div>
                                    
                                </div>
                            </div>
                            <span class="help-block">映射到本地的端口 如127.0.0.1:8080 则输入8080.购买后可任意修改</span>
                        </div>

                    </div>
                    <div class="form-group">
                        <label class="control-label col-sm-2">价格:</label>
                        <div class="col-sm-10">
                            <p class="form-control-static">
                                <span class="cash">0</span>&nbsp;<span class="yuan">元</span>
                            </p>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-8 col-sm-offset-2">
                            <button class="btn btn-success" type="submit" id="submitButton">免费购买</button>
                            <div class="spacer"></div>


                            <div class="alert alert-danger">
                                警告:禁止提供非法/违规/敏感信息或进行非法用途,一经发现立即封停账号,相关证据提交公安机关!禁止免流,翻墙,论坛等应用!


                            </div>
                            <div class="alert alert-info">
                                温馨提示,购买后,在我的隧道 -&gt; 配置 有更多选项可以自定义
                            </div>
                        </div>
                    </div>

                </fieldset>
            </form>

        </div>
    </div>






        </div>
    </div>

    <!-- Modal -->
    <div class="modal fade" id="iframeModal" tabindex="-1" role="dialog" aria-labelledby="" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="loading">正在加载...</div>
                <iframe src="" frameborder="0" width="100%" height="100%" scrolling="no" marginheight="0" marginwidth="0"></iframe>
            </div>
        </div>
    </div>
</div>
<!-- Modal Finish-->


<a href="javascript:void(0)" class="sidebar_switch on_switch ttip_r" title="Hide Sidebar">Sidebar switch</a>

<div class="sidebar">

    <div class="slimScrollDiv" style="position: relative; overflow: hidden; width: auto; height: 636px;"><div class="sidebar_inner_scroll" style="overflow: hidden; width: auto; height: 636px;">
        <div class="sidebar_inner">
            <form action="" class="input-group input-group-sm" method="post">


            </form>
            <div id="side_accordion" class="panel-group">

                                <div class="panel panel-default">
                    <div class="panel-heading">
                        <a href="#collapse_16" data-parent="#side_accordion" data-toggle="collapse" class="accordion-toggle">
                            <i class=" "></i> 我的隧道
                        </a>
                    </div>
                                        <div class="accordion-body collapse in" id="collapse_16">
                        <div class="panel-body">
                            <ul class="nav nav-pills nav-stacked">
                                                                <li class="active"><a href="/tunnel/buy" target="" class="leftMenuA"> 购买隧道</a>
                                </li>

                                
                                                                <li><a href="/tunnel/lists" target="" class="leftMenuA"> 我的隧道</a>
                                </li>

                                
                                                                <li><a href="/client/lists" target="" class="leftMenuA"> 聚合隧道</a>
                                </li>

                                
                                                                <li><a href="/subdomain/lists" target="" class="leftMenuA"> 二级域名</a>
                                </li>

                                
                                                                <li><a href="/flow/lists" target="" class="leftMenuA"> 流量包</a>
                                </li>

                                
                                                                <li><a href="/domain/lists" target="" class="leftMenuA"> 自主域名</a>
                                </li>

                                
                                
                            </ul>
                        </div>
                    </div>

                    
                </div>
                                <div class="panel panel-default">
                    <div class="panel-heading">
                        <a href="#collapse_17" data-parent="#side_accordion" data-toggle="collapse" class="accordion-toggle">
                            <i class=" "></i> 我的账户
                        </a>
                    </div>
                                        <div class="accordion-body collapse in" id="collapse_17">
                        <div class="panel-body">
                            <ul class="nav nav-pills nav-stacked">
                                                                <li><a href="/member/dashborad" target="" class="leftMenuA"> 账户信息</a>
                                </li>

                                
                                                                <li><a href="/member/realNameVerify" target="" class="leftMenuA"> 实名认证</a>
                                </li>

                                
                                                                <li><a href="/user/recharge" target="" class="leftMenuA"> 我要充值</a>
                                </li>

                                
                                                                <li><a href="/user/cash/log" target="" class="leftMenuA"> 充值日志</a>
                                </li>

                                
                                                                <li><a href="/user/pay/log" target="" class="leftMenuA"> 我的账单</a>
                                </li>

                                
                                                                <li><a href="/user/invoice" target="" class="leftMenuA"> 发票申请</a>
                                </li>

                                
                                                                <li><a href="/point/buy" target="" class="leftMenuA"> 购买积分</a>
                                </li>

                                
                                                                <li><a href="/member/pointlog" target="" class="leftMenuA"> 积分日志</a>
                                </li>

                                
                                                                <li><a href="/message" target="" class="leftMenuA"> 站内消息</a>
                                </li>

                                
                                                                <li><a href="/member/invitation" target="" class="leftMenuA"> 我要推广</a>
                                </li>

                                
                                
                            </ul>
                        </div>
                    </div>

                    
                </div>
                

            </div>

            <div class="push"></div>
        </div>

        <div class="sidebar_info">

        </div>
    </div><div class="slimScrollBar" style="background: rgb(0, 0, 0); width: 7px; position: absolute; top: 0px; opacity: 0.2; display: none; border-radius: 7px; z-index: 99; left: 1px; height: 636px;"></div><div class="slimScrollRail" style="width: 7px; height: 100%; position: absolute; top: 0px; display: none; border-radius: 7px; background: rgb(51, 51, 51); opacity: 0.2; z-index: 90; left: 1px;"></div></div>

</div><!-- main bootstrap js -->
    <script src="https://cdn.natapp.cn/assets/bootstrap/js/bootstrap.min.js?version=20250414"></script>
    <!-- bootstrap plugins -->
    <!-- <script src="https://cdn.natapp.cn/assets/admin/js/bootstrap.plugins.min.js?version=20250414"></script> -->


    <!-- sticky messages -->
    <script src="https://cdn.natapp.cn/assets/lib/sticky/sticky.min.js?version=20250414"></script>



    <!-- custom scrollbar -->
    <script src="https://cdn.natapp.cn/assets/lib/slimScroll/jquery.slimscroll.min.js?version=20250414"></script>

    <!-- to top -->
    <script src="https://cdn.natapp.cn/assets/lib/UItoTop/jquery.ui.totop.min.js?version=20250414"></script>






    <script src="https://cdn.natapp.cn/assets/natapp/js/gebo_common.js?version=20250414"></script>

    <!-- common functions -->


    <script src="https://cdn.natapp.cn/assets/lib/bootbox.js?version=20250414"></script>

    <!-- enhanced select -->








    <script src="https://cdn.natapp.cn/assets/lib/clipboard/dist/clipboard.min.js?version=20250414"></script>


<script src="https://cdn.natapp.cn/assets/js/st_global.js?version=20250414"></script>
<script src="https://cdn.natapp.cn/assets/js/st_tools.js?version=20250414"></script>
    <script src="https://cdn.natapp.cn/assets/natapp/js/st_common.js?version=20250414"></script>


 


<script>

    var APP_LOCALE = "zh_CN";
    var cdnUrl='https://cdn.natapp.cn/';
    var csrf_token='7zTXi0RPKtH6jGaMc7MyH3GPKbciPAvOdvpV3DhP';
    $(document).ready(function () {
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': '7zTXi0RPKtH6jGaMc7MyH3GPKbciPAvOdvpV3DhP'
            }
        });

        bootbox.setDefaults({

            locale: APP_LOCALE,
            closeButton: false


        });

        //* jQuery.browser.mobile (http://detectmobilebrowser.com/)
        //* jQuery.browser.mobile will be true if the browser is a mobile device
//        (function(a){jQuery.browser.mobile=/android.+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|symbian|treo|up\.(browser|link)|vodafone|wap|windows (ce|phone)|xda|xiino/i.test(a)||/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|e\-|e\/|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(di|rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|xda(\-|2|g)|yas\-|your|zeto|zte\-/i.test(a.substr(0,4))})(navigator.userAgent||navigator.vendor||window.opera);
//        //replace themeforest iframe
//        if(jQuery.browser.mobile) {
//            if (top !== self) top.location.href = self.location.href;
//        }
    });



</script>
<!--lang files-->





<script>


</script>

    <script src="https://cdn.natapp.cn/assets/lib/jquery-validation/jquery.validate.min.js?version=20250414"></script>
    <script src="https://cdn.natapp.cn/assets/lib/jquery-validation/validate-methods.js?version=20250414"></script>
    <script>
        var $saved_domain_rule, $saved_remote_port_rule;

        $(document).ready(function () {

            $validate = $("#buy_form").validate({
                onkeyup: false,
                focusCleanup: true,
                rules: {
                    "name": {
                        required: true,
                        rangelength: [1, 16],
                        stringCheck: true
                    },
                    "local_port": {
                        required: true,
                        range: [0, 65535],
                        digits: true
                    }
                },
                messages: {
                    "name": {
                        required: "请输入名称",
                        rangelength: "昵称为1-16个字",
                        stringCheck: '只能包含汉字,字母,数字,下划线',
                    },
                    "local_port": {
                        required: "请输入本地端口号",
                        range: "请输入0-65535 数字",
                        digits: '请输入0-65535 数字',

                    },
                },
                errorPlacement: function (error, element) {
                    element.closest(".input_control").siblings(".tipBox").find(".ok").hide();
                    element.closest(".input_control").siblings(".tipBox").find(".error").show().find(".txt").html(error.html());
                    element.closest(".form-group").addClass('has-error');

                },
                success: function (error, element) {
                    $(element).closest(".input_control").siblings(".tipBox").find(".error").hide();
                    $(element).closest(".input_control").siblings(".tipBox").find(".ok").show();
                    $(element).closest(".form-group").removeClass('has-error');


                },
                submitHandler: function (form) {
                    var button = $("#submitButton");
                    if (isEmpty($(button).attr('data-loading-text'))) {
                        $(button).attr('data-loading-text', '<i class="fa fa-refresh fa-spin"></i> Loading...');
                    }
                    $(button).button('loading');
                    form.submit();
                }
            });

            $saved_remote_port_rule = $('#remote_port').rules();
            $saved_domain_rule = $('#domain').rules();

        });
    </script>


<a href="#" id="toTop" style="display: inline;"><span id="toTopHover"></span>To Top</a></body></html>