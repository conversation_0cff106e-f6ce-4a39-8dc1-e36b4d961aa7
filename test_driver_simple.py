#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的驱动测试脚本
"""

import os
import subprocess

def test_driver():
    """测试驱动是否可用"""
    print("测试Edge驱动")
    print("=" * 30)
    
    driver_path = "msedgedriver.exe"
    
    # 检查文件是否存在
    if not os.path.exists(driver_path):
        print("❌ 未找到msedgedriver.exe文件")
        print("请先下载驱动文件到当前目录")
        return False
    
    print(f"✅ 找到驱动文件: {driver_path}")
    print(f"文件大小: {os.path.getsize(driver_path)} bytes")
    
    # 测试驱动版本
    try:
        result = subprocess.run([driver_path, "--version"], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ 驱动版本: {result.stdout.strip()}")
            return True
        else:
            print("❌ 驱动无法启动")
            return False
    except Exception as e:
        print(f"❌ 驱动测试失败: {e}")
        return False

if __name__ == "__main__":
    if test_driver():
        print("\n🎉 驱动测试成功！现在可以运行natapp_auto.py了")
    else:
        print("\n❌ 驱动测试失败，请检查驱动文件")
