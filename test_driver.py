#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Edge驱动测试脚本
用于诊断Edge驱动安装和配置问题
"""

import os
import sys
from selenium import webdriver
from selenium.webdriver.edge.service import Service
from selenium.webdriver.edge.options import Options
from webdriver_manager.microsoft import EdgeChromiumDriverManager

def test_webdriver_manager():
    """测试webdriver-manager自动下载驱动"""
    print("=" * 50)
    print("测试1: webdriver-manager自动下载驱动")
    print("=" * 50)
    
    try:
        print("正在下载Edge驱动...")
        driver_path = EdgeChromiumDriverManager().install()
        print(f"✅ 驱动下载成功: {driver_path}")
        
        # 检查文件是否存在
        if os.path.exists(driver_path):
            print(f"✅ 驱动文件存在: {driver_path}")
            print(f"文件大小: {os.path.getsize(driver_path)} bytes")
        else:
            print(f"❌ 驱动文件不存在: {driver_path}")
            return False
            
        return driver_path
    except Exception as e:
        print(f"❌ webdriver-manager下载失败: {str(e)}")
        return False

def test_system_driver():
    """测试系统PATH中的驱动"""
    print("\n" + "=" * 50)
    print("测试2: 系统PATH中的驱动")
    print("=" * 50)
    
    try:
        service = Service()  # 使用默认路径
        print("✅ 系统驱动服务创建成功")
        return service
    except Exception as e:
        print(f"❌ 系统驱动失败: {str(e)}")
        return False

def test_common_paths():
    """测试常见驱动路径"""
    print("\n" + "=" * 50)
    print("测试3: 常见驱动路径")
    print("=" * 50)
    
    common_paths = [
        r"C:\Program Files (x86)\Microsoft\Edge\Application\msedgedriver.exe",
        r"C:\Program Files\Microsoft\Edge\Application\msedgedriver.exe",
        r".\msedgedriver.exe",
        "msedgedriver.exe"
    ]
    
    for path in common_paths:
        print(f"检查路径: {path}")
        if os.path.exists(path):
            print(f"✅ 找到驱动: {path}")
            return path
        else:
            print(f"❌ 路径不存在: {path}")
    
    print("❌ 未找到任何常见路径的驱动")
    return False

def test_edge_browser():
    """测试Edge浏览器是否安装"""
    print("\n" + "=" * 50)
    print("测试4: Edge浏览器安装检查")
    print("=" * 50)
    
    edge_paths = [
        r"C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe",
        r"C:\Program Files\Microsoft\Edge\Application\msedge.exe"
    ]
    
    for path in edge_paths:
        print(f"检查Edge浏览器: {path}")
        if os.path.exists(path):
            print(f"✅ 找到Edge浏览器: {path}")
            return True
        else:
            print(f"❌ 路径不存在: {path}")
    
    print("❌ 未找到Edge浏览器")
    return False

def test_driver_creation(driver_path_or_service):
    """测试驱动创建"""
    print("\n" + "=" * 50)
    print("测试5: 创建WebDriver实例")
    print("=" * 50)
    
    try:
        edge_options = Options()
        edge_options.add_argument('--headless')
        edge_options.add_argument('--disable-gpu')
        edge_options.add_argument('--no-sandbox')
        edge_options.add_argument('--disable-dev-shm-usage')
        
        if isinstance(driver_path_or_service, str):
            service = Service(driver_path_or_service)
        else:
            service = driver_path_or_service
        
        print("正在创建WebDriver实例...")
        driver = webdriver.Edge(service=service, options=edge_options)
        print("✅ WebDriver创建成功")
        
        # 测试基本功能
        print("测试基本功能...")
        driver.get("https://www.baidu.com")
        print(f"✅ 页面加载成功: {driver.title}")
        
        driver.quit()
        print("✅ 驱动测试完成")
        return True
        
    except Exception as e:
        print(f"❌ WebDriver创建失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("Edge驱动诊断工具")
    print("=" * 50)
    
    # 检查Edge浏览器
    if not test_edge_browser():
        print("\n❌ 请先安装Microsoft Edge浏览器")
        return
    
    # 测试webdriver-manager
    driver_path = test_webdriver_manager()
    if driver_path:
        if test_driver_creation(driver_path):
            print("\n🎉 webdriver-manager方式成功！")
            return
    
    # 测试系统驱动
    system_service = test_system_driver()
    if system_service:
        if test_driver_creation(system_service):
            print("\n🎉 系统驱动方式成功！")
            return
    
    # 测试常见路径
    common_path = test_common_paths()
    if common_path:
        if test_driver_creation(common_path):
            print("\n🎉 常见路径方式成功！")
            return
    
    print("\n❌ 所有驱动方式都失败了")
    print("\n建议解决方案:")
    print("1. 确保已安装Microsoft Edge浏览器")
    print("2. 手动下载msedgedriver.exe到当前目录")
    print("3. 检查网络连接是否正常")
    print("4. 尝试更新webdriver-manager: pip install --upgrade webdriver-manager")

if __name__ == "__main__":
    main()
